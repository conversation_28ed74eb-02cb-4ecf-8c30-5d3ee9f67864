<?php declare(strict_types = 1);

namespace App\Api\Entity\Response;

use Apitte\Core\Mapping\Response\BasicEntity;
use App\PostType\Page\Model\Orm\CatalogTree;
use App\PostType\Page\Model\Orm\Tree;

class CategoryList extends BasicEntity
{

	/** @var CategoryListItem[] */
	public array $categories;

	public function __construct(
		array $categories
	)
	{
		$items = [];
		foreach ($categories as $item) {
			assert($item instanceof Tree);
			$items[] = new CategoryListItem($item);
		}

		$this->categories = $items;
	}

}
