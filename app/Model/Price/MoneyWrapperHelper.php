<?php declare(strict_types=1);

namespace App\Model\Price;

use App\Model\Time\CurrentDateTimeProvider;
use Brick\Math\BigDecimal;
use Brick\Math\RoundingMode;
use Brick\Money\Context\CustomContext;
use Brick\Money\Currency;
use Brick\Money\Money;
use Nextras\Dbal\Result\Row;
use Nextras\Dbal\Utils\DateTimeImmutable;

class MoneyWrapperHelper
{

	private DateTimeImmutable $now;

	public function __construct(
		CurrentDateTimeProvider $currentDateTimeProvider
	)
	{
		$this->now = $currentDateTimeProvider->getCurrentDateTime();
	}


	public function isValid(MoneyWrapper $moneyWrapper): bool
	{
		return ($moneyWrapper->from === null || $moneyWrapper->from <= $this->now)
			&& ($moneyWrapper->to === null || $moneyWrapper->to >= $this->now);
	}

	public function createEmpty(Currency $currency): MoneyWrapper
	{
		 return new MoneyWrapper(
			 money: Money::of(0, $currency->getCurrencyCode()),
			 vat: BigDecimal::of(0),
		 );
	}

	public function createFromRow(Row $priceRow, string $selectedCurrencyCode): MoneyWrapper
	{
		return new MoneyWrapper(
			money: Money::of($priceRow->price_amount, $selectedCurrencyCode, new CustomContext(4), RoundingMode::HALF_UP),
			vat: BigDecimal::of($priceRow->vat),
			from: $priceRow->validFrom,
			to: $priceRow->validTo,
		);
	}

}
