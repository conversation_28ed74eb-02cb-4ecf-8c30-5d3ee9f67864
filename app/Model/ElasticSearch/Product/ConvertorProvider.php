<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Product;

use App\Model\ElasticSearch\Product\Convertor\BaseData;
use App\Model\ElasticSearch\Product\Convertor\CategoryData;
use App\Model\ElasticSearch\Product\Convertor\CustomFeedData;
use App\Model\ElasticSearch\Product\Convertor\CustomFeedDataFlag;
use App\Model\ElasticSearch\Product\Convertor\DateTimeData;
use App\Model\ElasticSearch\Product\Convertor\DiscountData;
use App\Model\ElasticSearch\Product\Convertor\FulltextData;
use App\Model\ElasticSearch\Product\Convertor\ParameterData;
use App\Model\ElasticSearch\Product\Convertor\PriceData;
use App\Model\ElasticSearch\Product\Convertor\ScoreData;
use App\Model\ElasticSearch\Product\Convertor\StoreData;
use LogicException;

class ConvertorProvider
{

	private array $map;
	private array $mapForDelayed;

	public function __construct(
		BaseData $baseData,
		CategoryData $categoryData,
		ParameterData $parameterData,
		PriceData $priceData,
		StoreData $storeData,
		DateTimeData $dateTimeData,
		DiscountData $discountData,
		ScoreData $scoreData,
		CustomFeedData $customFeedData,
		CustomFeedDataFlag $customFeedDataFlag,
		FulltextData $fulltextData,
	)
	{
		$this->map = [];
		$this->mapForDelayed = [];
		foreach (func_get_args() as $convertor) {
			if ($convertor instanceof DelayedConvertor) {
				$this->mapForDelayed[$convertor::class] = $convertor;
			} else {
				$this->map[$convertor::class] = $convertor;
			}

		}
	}


	public function get(string $class): Convertor|DelayedConvertor
	{
		return $this->map[$class] ?? $this->mapForDelayed[$class] ?? throw new LogicException(sprintf("Missing convertor for '%s' class", $class));
	}


	public function getAll(): array
	{
		return array_values($this->map);
	}


	public function getAllLikeStrings(): array
	{
		return array_map(fn(object $item) => $item::class, $this->getAll());
	}

	public function getAllAndDelayed(): array
	{
		return array_values(array_merge($this->map, $this->mapForDelayed));
	}

}
