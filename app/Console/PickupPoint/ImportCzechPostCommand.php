<?php declare(strict_types = 1);

namespace App\Console\PickupPoint;

use App\Console\BaseCommand;
use App\Model\Orm\DeliveryMethod\BalikovnaPickup;
use App\Model\Orm\DeliveryMethod\CzechPostPickup;
use App\Model\Orm\Orm;
use GuzzleHttp\Client;
use Nette\Utils\FileSystem;
use Nette\Utils\Json;
use Nextras\Dbal\Connection;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Prewk\XmlStringStreamer;
use Prewk\XmlStringStreamer\Stream;
use Prewk\XmlStringStreamer\Parser;

#[AsCommand(
	name: 'pickupPoint:import:czechPost',
	description: 'Czech post branch import',
)]
// [AsCronTask(expression: '# 22 * * *', arguments: 'napostu', transports: 'cronCommands')]
// [AsCronTask(expression: '# 22 * * *', arguments: 'balikovny', transports: 'cronCommands')]
final class ImportCzechPostCommand extends BaseCommand
{

	private const ENDPOINTS = [
		'napostu' => 'http://napostu.ceskaposta.cz/vystupy/napostu_1.xml',
		'balikovny' => 'http://napostu.ceskaposta.cz/vystupy/balikovny.xml',
	];

	private const DELIVERY_METHOD_IDENT = [
		'napostu' => CzechPostPickup::ID,
		'balikovny' => BalikovnaPickup::ID,
	];

	public function __construct(
		private readonly Connection $connection,
		protected readonly Orm $orm,
	)
	{
		parent::__construct();
	}

	protected function configure(): void
	{
		$this->addArgument('type', InputArgument::REQUIRED, 'Type of import - [napostu, balikovny]');
	}



	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->start($input);
		$type = (string) $input->getArgument('type');

		if (!isset(self::ENDPOINTS[$type])) {
			$output->writeln('Type `' . $type . '` not exists.');
			return self::FAILURE;
		}

		if (($deliveryMethod = $this->orm->deliveryMethod->getBy(['deliveryMethodUniqueIdentifier' => self::DELIVERY_METHOD_IDENT[$type]])) === null) {
			$output->writeln('Delivery method with unique identifier ' . self::DELIVERY_METHOD_IDENT[$type] . ' not exists. Skipping import.');
			return self::FAILURE;
		}

		$output->writeln('Delivery method: ' . $deliveryMethod->name);

		$dir = TEMP_DIR . '/import';
		$file = $dir . '/' . $type . '_' . (new DateTimeImmutable())->format('Y-m-d_H-i-s') . '.xml';

		try {
			$client = new Client();
			$response = $client->get(self::ENDPOINTS[$type])->getBody()->getContents();

			FileSystem::createDir($dir);
			FileSystem::write($file, $response);

			$CHUNK_SIZE = 1024;
			$stream = new Stream\File($file, $CHUNK_SIZE);
			$parser = new Parser\UniqueNode(['uniqueNode' => 'row']);

			$streamer = new XmlStringStreamer($parser, $stream);

			$imported = [];
			$i = 0;
			while ($node = $streamer->getNode()) {
				if (!is_string($node)) {
					continue;
				}

				$branch = simplexml_load_string($node, null, LIBXML_NOCDATA);
				$branch = Json::encode($branch);
				$branch = Json::decode($branch, forceArrays: true);

				// unactive branch
				if ($type === 'napostu') {
					if ( ! (isset($branch['V_PROVOZU']) && $branch['V_PROVOZU'] === 'N')) {
						continue;
					}
				}

				$name = $type === 'napostu' ? $branch['NAZ_PROV'] : $branch['NAZEV'];
				$extId = md5($type . ($branch['PSC'] ?? '') . $name);

				$data = [
					'deliveryMethodId' => $deliveryMethod->id,
					'extId' => $extId,
					'name' => $name,
					'address' => $branch['ADRESA'],
					'syncTime' => new DateTimeImmutable(),
					'lat' => isset($branch['SOUR_Y_WGS84']) && is_string($branch['SOUR_Y_WGS84']) ? $branch['SOUR_Y_WGS84'] : null,
					'lng' => isset($branch['SOUR_X_WGS84']) && is_string($branch['SOUR_X_WGS84']) ? $branch['SOUR_X_WGS84'] : null,
					'openingHours' => Json::encode($this->getOpeningHours($branch, $type === 'napostu' ? 'OTV_DOBA' : 'OTEV_DOBY')),
				];

				$imported[] = $extId;

				$this->connection->query('INSERT INTO [pickup_point] %values ON DUPLICATE KEY UPDATE %set', $data, $data);

				$i++;
			}

			if ($imported !== []) {
				$this->connection->query('DELETE FROM [pickup_point] WHERE deliveryMethodId = %?i AND extId NOT IN %s[]', $deliveryMethod->id, $imported);
			}

			$output->writeln('Total processed: ' . $i);

		} catch (\Throwable $e) {
			$output->writeln($e->getMessage());
		}

		try {
			FileSystem::delete($file);
		} catch (\Throwable $e) {
			// do nothing
		}

		return $this->end(self::SUCCESS);
	}

	private function getOpeningHours(array $branch, string $element = 'OTV_DOBA'): array
	{
		$content = [];

		if (isset($branch[$element]['den']) && is_array($branch[$element]['den'])) {
			foreach ($branch[$element]['den'] as $day) {
				if ( ! empty($day['od_do'])) {

					$oh = [];
					if (isset($day['od_do'][0])) {
						foreach ($day['od_do'] as $dayArray) {
							$oh[] = ['from' => $dayArray['od'], 'to' => $dayArray['do']];
						}
					} else {
						$oh[] = ['from' => $day['od_do']['od'], 'to' => $day['od_do']['do']];
					}

					$content[$day['@attributes']['name']] = $oh;
					//$content[$day['@attributes']['name']] = isset($day['od_do'][0]) ? ['from' => $day['od_do']['od'], 'to' => $day['od_do']['do']] : [$day['od_do']];
				}
			}
		}

		return $content;
	}

}
