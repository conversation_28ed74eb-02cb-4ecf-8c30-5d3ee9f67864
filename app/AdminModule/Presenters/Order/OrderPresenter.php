<?php

declare(strict_types=1);

namespace App\AdminModule\Presenters\Order;

use App\AdminModule\Presenters\BasePresenter;
use App\AdminModule\Presenters\Order\Components\DataGrid\DataGrid;
use App\AdminModule\Presenters\Order\Components\DataGrid\DataGridFactory;
use App\Infrastructure\Latte\Filters;
use App\Model\Orm\Order\Order;
use App\Model\Orm\Order\OrderState;
use Nextras\Orm\Collection\ICollection;

final class OrderPresenter extends BasePresenter
{
	/**
	 * @var ICollection<Order>
	 */
	private ICollection $orders;

	private Order|null $order;

	public function __construct(
		private readonly DataGridFactory $dataGridFactory,
	)
	{
		parent::__construct();
	}

	public function actionDefault(): void
	{
		$this->orders = $this->orm->order
			->findBy(['state!=' => [OrderState::Draft, OrderState::SavedForLater]]);
			//->orderBy('placedAt', ICollection::DESC);
	}

	public function actionDetail(int $id): void
	{
		$this->order = $this->orm->order->getById($id);

		if ($this->order === null) {
			$this->redirect('default');
		}
	}

	public function renderDetail(): void
	{
		$this->template->add('order', $this->order);
	}

	protected function createComponentGrid(): DataGrid
	{
		return $this->dataGridFactory->create($this->orders);
	}
}
